// File-based Memory System for Chitu00
// Implements persistent storage using JSON files

import { 
  PersonalityTraits, 
  ConversationMemory, 
  SemanticMemory, 
  EpisodicMemory, 
  UserProfile, 
  PersonalityEvolution 
} from './memory-system';

export class FileMemorySystem {
  private dataDir = './chitu00-data';
  private conversationsFile = 'conversations.json';
  private semanticFile = 'semantic-memory.json';
  private episodicFile = 'episodic-memory.json';
  private userProfileFile = 'user-profile.json';
  private personalityHistoryFile = 'personality-history.json';
  private maxRetries = 3;
  private retryDelay = 100; // milliseconds

  constructor() {
    this.ensureDataDirectory();
  }

  private ensureDataDirectory(): void {
    // In a real implementation, this would create the directory
    // For now, we'll use localStorage in the browser
    try {
      if (typeof window !== 'undefined' && !localStorage.getItem('chitu00-initialized')) {
        localStorage.setItem('chitu00-initialized', new Date().toISOString());
      }
    } catch (error) {
      console.warn('Failed to initialize data directory:', error);
    }
  }

  private async withRetry<T>(operation: () => Promise<T> | T, context: string): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`Attempt ${attempt}/${this.maxRetries} failed for ${context}:`, error);

        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw new Error(`Failed after ${this.maxRetries} attempts in ${context}: ${lastError?.message}`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private safeLocalStorageOperation<T>(
    operation: () => T,
    fallback: T,
    context: string
  ): T {
    try {
      if (typeof window === 'undefined') {
        console.warn(`localStorage not available in ${context}, using fallback`);
        return fallback;
      }
      return operation();
    } catch (error) {
      console.error(`localStorage operation failed in ${context}:`, error);
      return fallback;
    }
  }

  // Conversation Memory Management
  async storeConversation(memory: ConversationMemory): Promise<void> {
    return this.withRetry(async () => {
      // Validate input
      if (!memory || !memory.id || !memory.userMessage) {
        throw new Error('Invalid conversation memory: missing required fields');
      }

      const conversations = await this.getRecentConversations(1000);

      // Check for duplicate IDs
      const existingIndex = conversations.findIndex(conv => conv.id === memory.id);
      if (existingIndex >= 0) {
        conversations[existingIndex] = memory; // Update existing
      } else {
        conversations.push(memory); // Add new
      }

      // Keep only the most recent 1000 conversations
      const trimmed = conversations.slice(-1000);

      return this.safeLocalStorageOperation(
        () => {
          localStorage.setItem('chitu00-conversations', JSON.stringify(trimmed));
        },
        undefined,
        'storeConversation'
      );
    }, 'storeConversation');
  }

  async getRecentConversations(limit: number = 10): Promise<ConversationMemory[]> {
    return this.withRetry(async () => {
      // Validate input
      if (limit < 0) {
        throw new Error('Limit must be non-negative');
      }

      const safeLimit = Math.min(Math.max(0, Math.floor(limit)), 10000); // Cap at 10k for safety

      return this.safeLocalStorageOperation(
        () => {
          const stored = localStorage.getItem('chitu00-conversations');
          if (!stored) {
            return [];
          }

          const conversations = JSON.parse(stored) as ConversationMemory[];

          // Validate data structure
          if (!Array.isArray(conversations)) {
            console.warn('Invalid conversations data structure, resetting');
            localStorage.removeItem('chitu00-conversations');
            return [];
          }

          return conversations
            .slice(-safeLimit)
            .map(conv => {
              try {
                return {
                  ...conv,
                  timestamp: new Date(conv.timestamp)
                };
              } catch (error) {
                console.warn('Invalid conversation timestamp:', conv.id, error);
                return {
                  ...conv,
                  timestamp: new Date() // Fallback to current time
                };
              }
            })
            .filter(conv => conv.id && conv.userMessage); // Filter out invalid entries
        },
        [],
        'getRecentConversations'
      );
    }, 'getRecentConversations');
  }

  async searchConversations(query: string, limit: number = 5): Promise<ConversationMemory[]> {
    try {
      const conversations = await this.getRecentConversations(1000);
      const lowerQuery = query.toLowerCase();
      
      const matches = conversations.filter(conv => 
        conv.userMessage.toLowerCase().includes(lowerQuery) ||
        conv.aiResponse.toLowerCase().includes(lowerQuery) ||
        conv.topics.some(topic => topic.toLowerCase().includes(lowerQuery))
      );
      
      return matches.slice(-limit);
    } catch (error) {
      console.error('Error searching conversations:', error);
      return [];
    }
  }

  // Semantic Memory Management
  async storeSemanticMemory(memory: SemanticMemory): Promise<void> {
    try {
      const memories = await this.getAllSemanticMemories();
      
      // Check if concept already exists and update it
      const existingIndex = memories.findIndex(m => m.concept === memory.concept);
      if (existingIndex >= 0) {
        memories[existingIndex] = {
          ...memories[existingIndex],
          ...memory,
          accessCount: memories[existingIndex].accessCount + 1,
          lastAccessed: new Date()
        };
      } else {
        memories.push(memory);
      }
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('chitu00-semantic', JSON.stringify(memories));
      }
    } catch (error) {
      console.error('Error storing semantic memory:', error);
    }
  }

  async getAllSemanticMemories(): Promise<SemanticMemory[]> {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('chitu00-semantic');
        if (stored) {
          return JSON.parse(stored).map((mem: any) => ({
            ...mem,
            lastAccessed: new Date(mem.lastAccessed)
          }));
        }
      }
      return [];
    } catch (error) {
      console.error('Error retrieving semantic memories:', error);
      return [];
    }
  }

  async getRelatedConcepts(concept: string): Promise<SemanticMemory[]> {
    try {
      const memories = await this.getAllSemanticMemories();
      const lowerConcept = concept.toLowerCase();
      
      return memories.filter(mem => 
        mem.concept.toLowerCase().includes(lowerConcept) ||
        mem.relatedConcepts.some(related => 
          related.toLowerCase().includes(lowerConcept)
        )
      );
    } catch (error) {
      console.error('Error finding related concepts:', error);
      return [];
    }
  }

  // Episodic Memory Management
  async storeEpisodicMemory(memory: EpisodicMemory): Promise<void> {
    try {
      const memories = await this.getSignificantEvents(1000);
      memories.push(memory);
      
      // Keep only the most significant 1000 events
      const sorted = memories.sort((a, b) => b.emotionalImpact - a.emotionalImpact);
      const trimmed = sorted.slice(0, 1000);
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('chitu00-episodic', JSON.stringify(trimmed));
      }
    } catch (error) {
      console.error('Error storing episodic memory:', error);
    }
  }

  async getSignificantEvents(limit: number = 5): Promise<EpisodicMemory[]> {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('chitu00-episodic');
        if (stored) {
          const memories = JSON.parse(stored).map((mem: any) => ({
            ...mem,
            timestamp: new Date(mem.timestamp)
          }));
          return memories.slice(0, limit);
        }
      }
      return [];
    } catch (error) {
      console.error('Error retrieving episodic memories:', error);
      return [];
    }
  }

  // User Profile Management
  async updateUserProfile(updates: Partial<UserProfile>): Promise<void> {
    try {
      const current = await this.getUserProfile();
      const updated = { ...current, ...updates };
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('chitu00-user-profile', JSON.stringify(updated));
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
    }
  }

  async getUserProfile(): Promise<UserProfile | null> {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('chitu00-user-profile');
        if (stored) {
          const profile = JSON.parse(stored);
          return {
            ...profile,
            interactionHistory: {
              ...profile.interactionHistory,
              emotionalTrends: profile.interactionHistory.emotionalTrends.map((trend: any) => ({
                ...trend,
                date: new Date(trend.date)
              }))
            }
          };
        }
      }
      return this.createDefaultProfile();
    } catch (error) {
      console.error('Error retrieving user profile:', error);
      return this.createDefaultProfile();
    }
  }

  private createDefaultProfile(): UserProfile {
    return {
      id: 'default-user',
      preferences: {},
      communicationStyle: 'friendly',
      interests: [],
      emotionalPatterns: {},
      interactionHistory: {
        totalInteractions: 0,
        averageSessionLength: 0,
        preferredTopics: [],
        emotionalTrends: []
      }
    };
  }

  // Personality Evolution Tracking
  async recordPersonalityChange(evolution: PersonalityEvolution): Promise<void> {
    try {
      const history = await this.getPersonalityHistory(100);
      history.push(evolution);
      
      // Keep only the most recent 100 changes
      const trimmed = history.slice(-100);
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('chitu00-personality-history', JSON.stringify(trimmed));
      }
    } catch (error) {
      console.error('Error recording personality change:', error);
    }
  }

  async getPersonalityHistory(limit: number = 10): Promise<PersonalityEvolution[]> {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('chitu00-personality-history');
        if (stored) {
          const history = JSON.parse(stored).map((evo: any) => ({
            ...evo,
            timestamp: new Date(evo.timestamp)
          }));
          return history.slice(-limit);
        }
      }
      return [];
    } catch (error) {
      console.error('Error retrieving personality history:', error);
      return [];
    }
  }

  // Memory Consolidation and Analysis
  async consolidateMemories(): Promise<void> {
    try {
      const conversations = await this.getRecentConversations(100);
      
      // Identify patterns and create semantic memories
      const patterns = this.identifyPatterns(conversations);
      
      // Create semantic memories from patterns
      for (const pattern of patterns) {
        if (pattern.type === 'topic' && pattern.frequency > 3) {
          await this.storeSemanticMemory({
            id: `topic-${pattern.value}`,
            concept: pattern.value,
            description: `User frequently discusses ${pattern.value}`,
            relatedConcepts: [],
            learningSource: 'conversation',
            confidence: Math.min(1.0, pattern.frequency / 10),
            lastAccessed: new Date(),
            accessCount: pattern.frequency
          });
        }
      }
      
      // Create episodic memories from significant conversations
      const significantConversations = conversations.filter(c => 
        this.calculateImportance(c) > 0.7
      );
      
      for (const conv of significantConversations) {
        await this.storeEpisodicMemory({
          id: conv.id + '-episodic',
          event: `Significant conversation about ${conv.topics.join(', ')}`,
          context: conv.userMessage.substring(0, 100),
          emotionalImpact: this.calculateImportance(conv),
          timestamp: conv.timestamp,
          participants: ['user', 'chitu00'],
          outcome: conv.aiResponse.substring(0, 100),
          lessons: [`User shows interest in ${conv.topics.join(', ')}`]
        });
      }
      
      // Update user profile based on patterns
      await this.updateUserProfileFromPatterns(patterns);
      
    } catch (error) {
      console.error('Error consolidating memories:', error);
    }
  }

  private identifyPatterns(conversations: ConversationMemory[]): any[] {
    const patterns = [];
    
    // Topic frequency analysis
    const topicCounts: Record<string, number> = {};
    conversations.forEach(conv => {
      conv.topics.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });
    
    Object.entries(topicCounts).forEach(([topic, count]) => {
      patterns.push({ type: 'topic', value: topic, frequency: count });
    });
    
    // Emotional pattern analysis
    const emotionCounts: Record<string, number> = {};
    conversations.forEach(conv => {
      emotionCounts[conv.userEmotion] = (emotionCounts[conv.userEmotion] || 0) + 1;
    });
    
    Object.entries(emotionCounts).forEach(([emotion, count]) => {
      patterns.push({ type: 'emotion', value: emotion, frequency: count });
    });
    
    return patterns;
  }

  private async updateUserProfileFromPatterns(patterns: any[]): Promise<void> {
    const profile = await this.getUserProfile();
    if (!profile) return;
    
    // Update interests based on topic patterns
    const topicPatterns = patterns.filter(p => p.type === 'topic');
    profile.interests = topicPatterns
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10)
      .map(p => p.value);
    
    // Update emotional patterns
    const emotionPatterns = patterns.filter(p => p.type === 'emotion');
    profile.emotionalPatterns = {};
    emotionPatterns.forEach(p => {
      profile.emotionalPatterns[p.value] = p.frequency;
    });
    
    await this.updateUserProfile(profile);
  }

  private calculateImportance(memory: ConversationMemory): number {
    let importance = 0.5; // Base importance
    
    // Emotional intensity increases importance
    if (memory.userEmotion === 'positive' || memory.userEmotion === 'negative') {
      importance += 0.2;
    }
    
    // Questions and learning moments are important
    if (memory.userMessage.includes('?')) {
      importance += 0.1;
    }
    
    // Personal topics are more important
    if (memory.topics.includes('personal')) {
      importance += 0.2;
    }
    
    return Math.min(1.0, importance);
  }

  // Backup and Restore
  async backupMemories(): Promise<string> {
    try {
      const backup = {
        conversations: await this.getRecentConversations(1000),
        semanticMemories: await this.getAllSemanticMemories(),
        episodicMemories: await this.getSignificantEvents(100),
        userProfile: await this.getUserProfile(),
        personalityHistory: await this.getPersonalityHistory(50),
        timestamp: new Date().toISOString()
      };
      
      return JSON.stringify(backup, null, 2);
    } catch (error) {
      console.error('Error creating backup:', error);
      return '{}';
    }
  }

  async restoreMemories(backupData: string): Promise<void> {
    try {
      const data = JSON.parse(backupData);
      
      if (typeof window !== 'undefined') {
        if (data.conversations) {
          localStorage.setItem('chitu00-conversations', JSON.stringify(data.conversations));
        }
        if (data.semanticMemories) {
          localStorage.setItem('chitu00-semantic', JSON.stringify(data.semanticMemories));
        }
        if (data.episodicMemories) {
          localStorage.setItem('chitu00-episodic', JSON.stringify(data.episodicMemories));
        }
        if (data.userProfile) {
          localStorage.setItem('chitu00-user-profile', JSON.stringify(data.userProfile));
        }
        if (data.personalityHistory) {
          localStorage.setItem('chitu00-personality-history', JSON.stringify(data.personalityHistory));
        }
      }
    } catch (error) {
      console.error('Error restoring memories:', error);
    }
  }

  // Clear all data (for testing or reset)
  async clearAllMemories(): Promise<void> {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('chitu00-conversations');
      localStorage.removeItem('chitu00-semantic');
      localStorage.removeItem('chitu00-episodic');
      localStorage.removeItem('chitu00-user-profile');
      localStorage.removeItem('chitu00-personality-history');
    }
  }
}
