{"name": "chitu00-personality-engine", "version": "1.0.0", "description": "Cognitive AI with Dynamic Personality & Mood Evolution", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"lucide-react": "^0.294.0", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "^5.0.0"}, "keywords": ["ai", "personality", "cognitive", "react", "typescript"], "author": "Chitu00 Team", "license": "MIT"}