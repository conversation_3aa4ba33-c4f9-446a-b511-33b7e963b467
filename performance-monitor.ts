// Performance Monitoring System for Chitu00
// Tracks system performance, memory usage, and response times

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  category: 'memory' | 'response' | 'storage' | 'personality' | 'ui';
}

export interface PerformanceAlert {
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  metric: string;
  threshold: number;
  actual: number;
  timestamp: Date;
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'critical';
  score: number; // 0-100
  metrics: PerformanceMetric[];
  alerts: PerformanceAlert[];
  recommendations: string[];
}

export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private alerts: PerformanceAlert[] = [];
  private maxMetrics = 1000;
  private maxAlerts = 100;
  
  // Performance thresholds
  private thresholds = {
    responseTime: { warning: 200, critical: 1000 }, // milliseconds
    memoryUsage: { warning: 1024 * 1024, critical: 5 * 1024 * 1024 }, // bytes
    storageSize: { warning: 500 * 1024, critical: 1024 * 1024 }, // bytes
    errorRate: { warning: 0.05, critical: 0.1 }, // percentage
    personalityStability: { warning: 0.3, critical: 0.5 } // change rate
  };

  // Track a performance metric
  recordMetric(name: string, value: number, unit: string, category: PerformanceMetric['category']): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      category
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Check thresholds and generate alerts
    this.checkThresholds(metric);
  }

  // Time a function execution
  async timeFunction<T>(
    name: string, 
    category: PerformanceMetric['category'],
    fn: () => Promise<T> | T
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      this.recordMetric(name, duration, 'ms', category);
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(name + '_error', duration, 'ms', category);
      throw error;
    }
  }

  // Monitor memory usage
  recordMemoryUsage(): void {
    if (typeof window !== 'undefined') {
      // Calculate localStorage usage
      let totalSize = 0;
      for (let key in localStorage) {
        if (key.startsWith('chitu00-')) {
          totalSize += localStorage[key].length * 2; // UTF-16 encoding
        }
      }
      
      this.recordMetric('localStorage_usage', totalSize, 'bytes', 'memory');

      // Browser memory API (if available)
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        this.recordMetric('heap_used', memInfo.usedJSHeapSize, 'bytes', 'memory');
        this.recordMetric('heap_total', memInfo.totalJSHeapSize, 'bytes', 'memory');
        this.recordMetric('heap_limit', memInfo.jsHeapSizeLimit, 'bytes', 'memory');
      }
    }
  }

  // Get recent metrics
  getMetrics(category?: PerformanceMetric['category'], limit: number = 100): PerformanceMetric[] {
    let filtered = this.metrics;
    
    if (category) {
      filtered = filtered.filter(m => m.category === category);
    }
    
    return filtered.slice(-limit);
  }

  // Get recent alerts
  getAlerts(level?: PerformanceAlert['level'], limit: number = 50): PerformanceAlert[] {
    let filtered = this.alerts;
    
    if (level) {
      filtered = filtered.filter(a => a.level === level);
    }
    
    return filtered.slice(-limit);
  }

  // Calculate system health
  getSystemHealth(): SystemHealth {
    const recentMetrics = this.getMetrics(undefined, 100);
    const recentAlerts = this.getAlerts(undefined, 20);
    
    // Calculate health score
    let score = 100;
    
    // Deduct points for alerts
    recentAlerts.forEach(alert => {
      switch (alert.level) {
        case 'critical': score -= 20; break;
        case 'error': score -= 10; break;
        case 'warning': score -= 5; break;
        case 'info': score -= 1; break;
      }
    });

    // Deduct points for poor performance
    const avgResponseTime = this.getAverageMetric('response_time', 'response');
    if (avgResponseTime > this.thresholds.responseTime.warning) {
      score -= 10;
    }
    if (avgResponseTime > this.thresholds.responseTime.critical) {
      score -= 20;
    }

    score = Math.max(0, Math.min(100, score));

    // Determine overall health
    let overall: SystemHealth['overall'];
    if (score >= 80) overall = 'healthy';
    else if (score >= 50) overall = 'degraded';
    else overall = 'critical';

    // Generate recommendations
    const recommendations = this.generateRecommendations(recentMetrics, recentAlerts);

    return {
      overall,
      score,
      metrics: recentMetrics,
      alerts: recentAlerts,
      recommendations
    };
  }

  // Clear old data
  cleanup(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoffTime);
    this.alerts = this.alerts.filter(a => a.timestamp > cutoffTime);
  }

  // Export performance data
  exportData(): string {
    return JSON.stringify({
      metrics: this.metrics,
      alerts: this.alerts,
      thresholds: this.thresholds,
      timestamp: new Date()
    }, null, 2);
  }

  // Import performance data
  importData(data: string): void {
    try {
      const parsed = JSON.parse(data);
      
      if (parsed.metrics && Array.isArray(parsed.metrics)) {
        this.metrics = parsed.metrics.map((m: any) => ({
          ...m,
          timestamp: new Date(m.timestamp)
        }));
      }
      
      if (parsed.alerts && Array.isArray(parsed.alerts)) {
        this.alerts = parsed.alerts.map((a: any) => ({
          ...a,
          timestamp: new Date(a.timestamp)
        }));
      }
      
      if (parsed.thresholds) {
        this.thresholds = { ...this.thresholds, ...parsed.thresholds };
      }
    } catch (error) {
      console.error('Failed to import performance data:', error);
    }
  }

  private checkThresholds(metric: PerformanceMetric): void {
    const { name, value } = metric;
    
    // Check response time thresholds
    if (name.includes('response') || name.includes('time')) {
      if (value > this.thresholds.responseTime.critical) {
        this.addAlert('critical', `Critical response time: ${Math.round(value)}ms`, name, this.thresholds.responseTime.critical, value);
      } else if (value > this.thresholds.responseTime.warning) {
        this.addAlert('warning', `Slow response time: ${Math.round(value)}ms`, name, this.thresholds.responseTime.warning, value);
      }
    }
    
    // Check memory thresholds
    if (name.includes('memory') || name.includes('usage')) {
      if (value > this.thresholds.memoryUsage.critical) {
        this.addAlert('critical', `Critical memory usage: ${Math.round(value / 1024)}KB`, name, this.thresholds.memoryUsage.critical, value);
      } else if (value > this.thresholds.memoryUsage.warning) {
        this.addAlert('warning', `High memory usage: ${Math.round(value / 1024)}KB`, name, this.thresholds.memoryUsage.warning, value);
      }
    }
  }

  private addAlert(level: PerformanceAlert['level'], message: string, metric: string, threshold: number, actual: number): void {
    const alert: PerformanceAlert = {
      level,
      message,
      metric,
      threshold,
      actual,
      timestamp: new Date()
    };

    this.alerts.push(alert);
    
    // Keep only recent alerts
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(-this.maxAlerts);
    }

    // Log critical alerts
    if (level === 'critical' || level === 'error') {
      console.error(`Performance Alert [${level.toUpperCase()}]: ${message}`);
    } else if (level === 'warning') {
      console.warn(`Performance Alert [${level.toUpperCase()}]: ${message}`);
    }
  }

  private getAverageMetric(namePattern: string, category?: PerformanceMetric['category']): number {
    const filtered = this.metrics.filter(m => {
      const matchesName = m.name.toLowerCase().includes(namePattern.toLowerCase());
      const matchesCategory = !category || m.category === category;
      return matchesName && matchesCategory;
    });

    if (filtered.length === 0) return 0;
    
    const sum = filtered.reduce((total, m) => total + m.value, 0);
    return sum / filtered.length;
  }

  private generateRecommendations(metrics: PerformanceMetric[], alerts: PerformanceAlert[]): string[] {
    const recommendations: string[] = [];
    
    // Check for critical alerts
    const criticalAlerts = alerts.filter(a => a.level === 'critical');
    if (criticalAlerts.length > 0) {
      recommendations.push('🚨 Address critical performance issues immediately');
    }

    // Check response time
    const avgResponseTime = this.getAverageMetric('response', 'response');
    if (avgResponseTime > this.thresholds.responseTime.warning) {
      recommendations.push('⚡ Optimize response generation for better performance');
    }

    // Check memory usage
    const avgMemoryUsage = this.getAverageMetric('memory', 'memory');
    if (avgMemoryUsage > this.thresholds.memoryUsage.warning) {
      recommendations.push('🧠 Consider memory cleanup or data consolidation');
    }

    // Check for frequent errors
    const errorMetrics = metrics.filter(m => m.name.includes('error'));
    if (errorMetrics.length > metrics.length * 0.1) {
      recommendations.push('🔧 High error rate detected - review error handling');
    }

    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('✅ System performance is optimal');
    } else {
      recommendations.push('📊 Monitor performance metrics regularly');
      recommendations.push('🔄 Run system cleanup periodically');
    }

    return recommendations;
  }
}
