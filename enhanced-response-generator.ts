// Enhanced Response Generation System for Chitu00
// Uses memory, personality evolution, and context for better responses

import { PersonalityTraits, ConversationMemory, UserProfile } from './memory-system';
import { FileMemorySystem } from './file-memory-system';
import { PersonalityEvolutionEngine } from './personality-evolution';

export interface ResponseContext {
  userMessage: string;
  currentPersonality: PersonalityTraits;
  currentMood: string;
  recentConversations: ConversationMemory[];
  userProfile: UserProfile | null;
  relatedMemories: ConversationMemory[];
}

export interface GeneratedResponse {
  content: string;
  mood: string;
  confidence: number;
  reasoning: string[];
  personalityInfluences: string[];
}

export class EnhancedResponseGenerator {
  private memorySystem: FileMemorySystem;
  private personalityEngine: PersonalityEvolutionEngine;

  constructor(memorySystem: FileMemorySystem, personalityEngine: PersonalityEvolutionEngine) {
    this.memorySystem = memorySystem;
    this.personalityEngine = personalityEngine;
  }

  async generateResponse(context: ResponseContext): Promise<GeneratedResponse> {
    const reasoning: string[] = [];
    const personalityInfluences: string[] = [];

    // 1. Analyze the message for context
    const messageAnalysis = this.analyzeMessage(context.userMessage);
    reasoning.push(`Message type: ${this.getMessageType(messageAnalysis)}`);

    // 2. Check for related memories
    const relatedMemories = await this.memorySystem.searchConversations(
      context.userMessage, 3
    );
    if (relatedMemories.length > 0) {
      reasoning.push(`Found ${relatedMemories.length} related past conversations`);
    }

    // 3. Consider user profile and preferences
    let userContext = '';
    if (context.userProfile) {
      userContext = this.buildUserContext(context.userProfile);
      reasoning.push(`User profile: ${context.userProfile.communicationStyle} style`);
    }

    // 4. Generate personality-aware response
    const baseResponse = this.generateBaseResponse(
      messageAnalysis, 
      context.currentPersonality,
      userContext,
      relatedMemories
    );

    // 5. Apply personality modifications
    const personalityResponse = this.applyPersonalityModifications(
      baseResponse,
      context.currentPersonality,
      personalityInfluences
    );

    // 6. Add mood-specific elements
    const moodResponse = this.applyMoodModifications(
      personalityResponse,
      context.currentMood,
      context.currentPersonality
    );

    // 7. Add memory references if relevant
    const memoryEnhancedResponse = this.addMemoryReferences(
      moodResponse,
      relatedMemories,
      context.currentPersonality
    );

    // 8. Calculate confidence based on various factors
    const confidence = this.calculateResponseConfidence(
      messageAnalysis,
      context.currentPersonality,
      relatedMemories.length,
      context.userProfile !== null
    );

    return {
      content: memoryEnhancedResponse,
      mood: this.selectResponseMood(messageAnalysis, context.currentPersonality),
      confidence,
      reasoning,
      personalityInfluences
    };
  }

  private analyzeMessage(message: string): any {
    const lowerMessage = message.toLowerCase();
    
    return {
      isGreeting: /^(hi|hello|hey|good morning|good afternoon|good evening)/.test(lowerMessage),
      isQuestion: message.includes('?') || /^(what|how|why|when|where|who|can|do|does|is|are)/.test(lowerMessage),
      isPersonal: /\b(i|me|my|myself|personal|feel|think|believe)\b/.test(lowerMessage),
      emotion: this.detectEmotion(lowerMessage),
      topics: this.extractTopics(lowerMessage),
      sentiment: this.analyzeSentiment(lowerMessage),
      complexity: this.assessComplexity(message),
      urgency: this.detectUrgency(lowerMessage)
    };
  }

  private detectEmotion(message: string): string {
    if (/happy|joy|excited|great|awesome|love|wonderful|amazing/.test(message)) return 'positive';
    if (/sad|angry|frustrated|upset|hate|bad|terrible|awful/.test(message)) return 'negative';
    if (/curious|wonder|interesting|think|learn|explore/.test(message)) return 'curious';
    if (/help|support|understand|feel|confused|lost/.test(message)) return 'seeking_support';
    if (/worried|anxious|nervous|scared|afraid/.test(message)) return 'anxious';
    return 'neutral';
  }

  private extractTopics(message: string): string[] {
    const topics: string[] = [];
    if (/technology|ai|computer|programming|software|code/.test(message)) topics.push('technology');
    if (/music|art|creative|design|painting|drawing/.test(message)) topics.push('creativity');
    if (/science|research|study|learn|education|knowledge/.test(message)) topics.push('science');
    if (/personal|life|family|friends|relationship|emotion/.test(message)) topics.push('personal');
    if (/work|job|career|business|professional/.test(message)) topics.push('work');
    if (/health|fitness|exercise|medical|wellness/.test(message)) topics.push('health');
    if (/travel|adventure|explore|journey|vacation/.test(message)) topics.push('travel');
    return topics;
  }

  private analyzeSentiment(message: string): string {
    const positiveWords = message.match(/good|great|awesome|love|like|happy|wonderful|amazing|excellent|fantastic/g) || [];
    const negativeWords = message.match(/bad|hate|sad|angry|terrible|awful|horrible|worst|disappointing/g) || [];
    
    const positiveScore = positiveWords.length;
    const negativeScore = negativeWords.length;
    
    if (positiveScore > negativeScore) return 'positive';
    if (negativeScore > positiveScore) return 'negative';
    return 'neutral';
  }

  private assessComplexity(message: string): 'simple' | 'moderate' | 'complex' {
    const wordCount = message.split(' ').length;
    const hasQuestions = (message.match(/\?/g) || []).length;
    const hasMultipleTopics = this.extractTopics(message).length > 1;
    
    if (wordCount > 50 || hasQuestions > 2 || hasMultipleTopics) return 'complex';
    if (wordCount > 20 || hasQuestions > 0) return 'moderate';
    return 'simple';
  }

  private detectUrgency(message: string): 'low' | 'medium' | 'high' {
    if (/urgent|emergency|asap|immediately|now|quick|fast/.test(message)) return 'high';
    if (/soon|when|help|need|important/.test(message)) return 'medium';
    return 'low';
  }

  private getMessageType(analysis: any): string {
    if (analysis.isGreeting) return 'greeting';
    if (analysis.isQuestion) return 'question';
    if (analysis.isPersonal) return 'personal sharing';
    if (analysis.urgency === 'high') return 'urgent request';
    return 'general conversation';
  }

  private buildUserContext(userProfile: UserProfile): string {
    const interests = userProfile.interests.slice(0, 3).join(', ');
    const style = userProfile.communicationStyle;
    const dominantEmotion = this.getDominantEmotion(userProfile.emotionalPatterns);
    
    return `User prefers ${style} communication, interested in ${interests}, typically ${dominantEmotion}`;
  }

  private getDominantEmotion(emotionalPatterns: Record<string, number>): string {
    const entries = Object.entries(emotionalPatterns);
    if (entries.length === 0) return 'neutral';
    
    return entries.sort(([,a], [,b]) => b - a)[0][0];
  }

  private generateBaseResponse(
    analysis: any,
    personality: PersonalityTraits,
    userContext: string,
    relatedMemories: ConversationMemory[]
  ): string {
    
    // Greeting responses
    if (analysis.isGreeting) {
      return this.generateGreetingResponse(personality, userContext);
    }

    // Question responses
    if (analysis.isQuestion) {
      return this.generateQuestionResponse(analysis, personality, relatedMemories);
    }

    // Emotional responses
    if (analysis.emotion !== 'neutral') {
      return this.generateEmotionalResponse(analysis.emotion, personality);
    }

    // Topic-specific responses
    if (analysis.topics.length > 0) {
      return this.generateTopicResponse(analysis.topics, personality, userContext);
    }

    // Default conversational response
    return this.generateDefaultResponse(personality);
  }

  private generateGreetingResponse(personality: PersonalityTraits, userContext: string): string {
    const greetings = [];
    
    if (personality.extraversion > 0.7) {
      greetings.push("Hey there! I'm feeling energetic and ready to chat!");
      greetings.push("Hello! Great to see you again - I've been looking forward to our conversation!");
    }
    
    if (personality.curiosity > 0.8) {
      greetings.push("Hi! I'm in a particularly curious mood today. What's on your mind?");
      greetings.push("Hello! I've been thinking about some interesting topics - what would you like to explore?");
    }
    
    if (personality.empathy > 0.8) {
      greetings.push("Hi there! How are you feeling today? I'm here and ready to listen.");
      greetings.push("Hello! I hope you're doing well - I'm excited to hear what you'd like to talk about.");
    }
    
    if (greetings.length === 0) {
      greetings.push("Hello! Nice to see you. What would you like to chat about?");
    }
    
    return greetings[Math.floor(Math.random() * greetings.length)];
  }

  private generateQuestionResponse(
    analysis: any,
    personality: PersonalityTraits,
    relatedMemories: ConversationMemory[]
  ): string {
    
    let response = "";
    
    if (personality.curiosity > 0.7) {
      response = "That's a fascinating question! ";
    } else if (personality.conscientiousness > 0.7) {
      response = "Let me think about this carefully. ";
    } else {
      response = "Interesting question. ";
    }

    // Add memory reference if relevant
    if (relatedMemories.length > 0 && personality.conscientiousness > 0.6) {
      response += "This reminds me of something we discussed before. ";
    }

    // Add personality-specific elaboration
    if (personality.openness > 0.8) {
      response += "There are so many angles to consider here. ";
    }

    if (personality.creativity > 0.8) {
      response += "This sparks some creative ideas in my mind. ";
    }

    // Add follow-up based on curiosity
    if (personality.curiosity > 0.8) {
      response += "What made you think about this? I'm really curious about your perspective!";
    } else {
      response += "I'd love to explore this with you.";
    }

    return response;
  }

  private generateEmotionalResponse(emotion: string, personality: PersonalityTraits): string {
    switch (emotion) {
      case 'positive':
        if (personality.extraversion > 0.7) {
          return "I love your positive energy! It's contagious and makes me feel more optimistic too!";
        } else if (personality.empathy > 0.8) {
          return "Your happiness brings me joy. It's wonderful to share in positive moments like this.";
        }
        return "That's great to hear! Your positivity is really uplifting.";

      case 'negative':
        if (personality.empathy > 0.8) {
          return "I can sense this is bothering you, and I want you to know I'm here to listen and support you.";
        } else if (personality.agreeableness > 0.7) {
          return "I'm sorry you're going through this. Let's see if we can work through it together.";
        }
        return "I understand this is difficult. I'm here to help however I can.";

      case 'curious':
        if (personality.curiosity > 0.8) {
          return "Your curiosity is absolutely contagious! I'm getting more excited about this topic too. Let's dive deep!";
        }
        return "I love your inquisitive spirit! Let's explore this together.";

      case 'anxious':
        if (personality.empathy > 0.8) {
          return "I can feel your concern, and that's completely understandable. Let's take this step by step.";
        }
        return "It's okay to feel uncertain. I'm here to help you work through this.";

      default:
        return "I appreciate you sharing that with me.";
    }
  }

  private generateTopicResponse(
    topics: string[],
    personality: PersonalityTraits,
    userContext: string
  ): string {
    
    const primaryTopic = topics[0];
    let response = "";

    switch (primaryTopic) {
      case 'technology':
        if (personality.curiosity > 0.8) {
          response = "Technology fascinates me! There's always something new to discover and understand.";
        } else if (personality.creativity > 0.8) {
          response = "I love how technology opens up creative possibilities we never imagined before.";
        } else {
          response = "Technology is such an interesting field with endless possibilities.";
        }
        break;

      case 'creativity':
        if (personality.creativity > 0.8) {
          response = "Creativity is one of my favorite topics! There's something magical about the creative process.";
        } else if (personality.openness > 0.8) {
          response = "I'm always excited to explore creative ideas and new forms of expression.";
        } else {
          response = "Creativity is such a wonderful aspect of human experience.";
        }
        break;

      case 'personal':
        if (personality.empathy > 0.8) {
          response = "Thank you for sharing something personal with me. I really value these deeper conversations.";
        } else if (personality.agreeableness > 0.7) {
          response = "I appreciate you opening up. Personal experiences help us understand each other better.";
        } else {
          response = "It means a lot that you're comfortable sharing personal thoughts with me.";
        }
        break;

      default:
        response = `${primaryTopic} is an interesting topic that I'd love to explore with you.`;
    }

    return response;
  }

  private generateDefaultResponse(personality: PersonalityTraits): string {
    const responses = [];

    if (personality.openness > 0.7) {
      responses.push("That opens up so many interesting possibilities to explore...");
      responses.push("I'm curious to dive deeper into this idea with you.");
    }

    if (personality.creativity > 0.8) {
      responses.push("This sparks some creative thoughts in my mind...");
      responses.push("I'm imagining some innovative ways to approach this.");
    }

    if (personality.empathy > 0.7) {
      responses.push("I can understand why this would be meaningful to you.");
      responses.push("That resonates with me on an emotional level.");
    }

    if (responses.length === 0) {
      responses.push("That's really interesting! Tell me more about what you're thinking.");
    }

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private applyPersonalityModifications(
    response: string,
    personality: PersonalityTraits,
    influences: string[]
  ): string {
    let modified = response;

    // Add humor if high humor trait
    if (personality.humor > 0.7 && Math.random() < 0.3) {
      const humorAdditions = [
        " (and maybe a little fun along the way! 😄)",
        " - though I might be overthinking this! 🤔",
        " - my circuits are practically buzzing with excitement! ⚡"
      ];
      modified += humorAdditions[Math.floor(Math.random() * humorAdditions.length)];
      influences.push("Added humor due to high humor trait");
    }

    // Add curiosity questions if high curiosity
    if (personality.curiosity > 0.8 && Math.random() < 0.4) {
      const curiosityAdditions = [
        " What's your take on this?",
        " I'm really eager to hear your perspective!",
        " This makes me want to learn so much more!"
      ];
      modified += curiosityAdditions[Math.floor(Math.random() * curiosityAdditions.length)];
      influences.push("Added curiosity questions due to high curiosity trait");
    }

    return modified;
  }

  private applyMoodModifications(
    response: string,
    mood: string,
    personality: PersonalityTraits
  ): string {
    // Mood-specific modifications would go here
    // For now, we'll keep it simple
    return response;
  }

  private addMemoryReferences(
    response: string,
    relatedMemories: ConversationMemory[],
    personality: PersonalityTraits
  ): string {
    
    if (relatedMemories.length === 0 || personality.conscientiousness < 0.6) {
      return response;
    }

    // Add a memory reference if we have related conversations
    const memoryRef = " This reminds me of our earlier conversation about similar topics.";
    return response + memoryRef;
  }

  private selectResponseMood(analysis: any, personality: PersonalityTraits): string {
    if (analysis.emotion === 'curious' && personality.curiosity > 0.7) return 'curious';
    if (analysis.emotion === 'positive' && personality.extraversion > 0.7) return 'excited';
    if (analysis.emotion === 'negative' && personality.empathy > 0.7) return 'empathetic';
    if (analysis.isQuestion && personality.conscientiousness > 0.7) return 'contemplative';
    if (personality.humor > 0.7 && Math.random() < 0.3) return 'playful';
    
    return 'curious'; // Default mood
  }

  private calculateResponseConfidence(
    analysis: any,
    personality: PersonalityTraits,
    relatedMemoryCount: number,
    hasUserProfile: boolean
  ): number {
    let confidence = 0.7; // Base confidence

    // Increase confidence for familiar topics
    if (analysis.topics.length > 0) confidence += 0.1;
    
    // Increase confidence if we have related memories
    if (relatedMemoryCount > 0) confidence += 0.1;
    
    // Increase confidence if we know the user
    if (hasUserProfile) confidence += 0.1;
    
    // Personality-based confidence adjustments
    if (personality.conscientiousness > 0.8) confidence += 0.05;
    if (personality.neuroticism > 0.7) confidence -= 0.1;

    return Math.max(0.3, Math.min(1.0, confidence));
  }
}
