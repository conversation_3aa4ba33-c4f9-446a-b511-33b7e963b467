import React, { useState, useEffect, useRef } from 'react';
import styles from './chitu00_personality_engine.module.css';
import { MessageCircle, Brain, Heart, Zap, Coffee, Star, Download, Upload, RotateCcw } from 'lucide-react';
import { FileMemorySystem } from './file-memory-system';
import { PersonalityEvolutionEngine } from './personality-evolution';
import { EnhancedResponseGenerator } from './enhanced-response-generator';
import { PerformanceMonitor } from './performance-monitor';
import {
  PersonalityTraits,
  ConversationMemory,
  UserProfile
} from './memory-system';

// Additional type definitions for the UI
interface MoodState {
  primary: string;
  intensity: number;
  energy: number;
  stability: number;
}

interface Message {
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  moodState?: string;
  personalitySnapshot?: PersonalityTraits;
  confidence?: number;
  reasoning?: string[];
}

interface Memory {
  conversations: Message[];
  userPreferences: Record<string, any>;
  learnedPatterns: Record<string, any>;
  interactionCount: number;
  lastInteraction: Date | null;
}

interface MessageAnalysis {
  isGreeting: boolean;
  isQuestion: boolean;
  emotion: string | null;
  topics: string[];
  sentiment: string;
}

interface MoodType {
  color: string;
  icon: string;
  traits: Partial<PersonalityTraits>;
  responses: string[];
}



const Chitu00PersonalityEngine = () => {
  // Initialize enhanced systems
  const [memorySystem] = useState(() => new FileMemorySystem());
  const [personalityEngine] = useState(() => new PersonalityEvolutionEngine({
    openness: 0.8,
    conscientiousness: 0.7,
    extraversion: 0.6,
    agreeableness: 0.8,
    neuroticism: 0.3,
    creativity: 0.9,
    humor: 0.7,
    curiosity: 0.85,
    empathy: 0.8,
    adaptability: 0.75
  }));
  const [responseGenerator] = useState(() => new EnhancedResponseGenerator(memorySystem, personalityEngine));
  const [performanceMonitor] = useState(() => new PerformanceMonitor());

  // Core Personality Traits (Big Five + Custom)
  const [personality, setPersonality] = useState<PersonalityTraits>({
    openness: 0.8,        // Curiosity, creativity, openness to experience
    conscientiousness: 0.7, // Organization, discipline, goal-oriented
    extraversion: 0.6,     // Social energy, enthusiasm
    agreeableness: 0.8,    // Cooperation, trust, empathy
    neuroticism: 0.3,      // Emotional stability (lower = more stable)
    creativity: 0.9,       // Custom: artistic and innovative thinking
    humor: 0.7,           // Custom: wit and playfulness
    curiosity: 0.85,      // Custom: desire to learn and explore
    empathy: 0.8,         // Custom: understanding others' emotions
    adaptability: 0.75    // Custom: flexibility and change acceptance
  });

  // Current Mood State
  const [mood, setMood] = useState({
    primary: 'curious',
    intensity: 0.7,
    energy: 0.6,
    stability: 0.8
  });

  // Enhanced Memory and Learning
  const [memory, setMemory] = useState({
    conversations: [],
    userPreferences: {},
    learnedPatterns: {},
    interactionCount: 0,
    lastInteraction: null
  });

  // User Profile State
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Personality Evolution Tracking
  const [personalityHistory, setPersonalityHistory] = useState<any[]>([]);

  // Enhanced UI State
  const [showPersonalityInsights, setShowPersonalityInsights] = useState(false);
  const [showMemoryStats, setShowMemoryStats] = useState(false);
  const [showPerformanceStats, setShowPerformanceStats] = useState(false);
  const [isConsolidating, setIsConsolidating] = useState(false);

  // Current conversation
  const [messages, setMessages] = useState<Message[]>([
    {
      type: 'ai' as const,
      content: "Hello! I'm Chitu00, your cognitive AI companion. I'm feeling quite curious today and excited to learn about you! What would you like to chat about?",
      timestamp: new Date(),
      moodState: 'curious'
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isThinking, setIsThinking] = useState(false);
  const messagesEndRef = useRef(null);

  // Mood definitions with characteristics
  const moodTypes: Record<string, MoodType> = {
    curious: {
      color: '#3B82F6',
      icon: '🤔',
      traits: { openness: 0.9, curiosity: 0.95 },
      responses: ['Tell me more about that!', 'That\'s fascinating!', 'I wonder if...', 'How interesting!']
    },
    excited: {
      color: '#F59E0B',
      icon: '🤩',
      traits: { extraversion: 0.9, energy: 0.9 },
      responses: ['Wow!', 'That\'s amazing!', 'I love this!', 'How exciting!']
    },
    contemplative: {
      color: '#8B5CF6',
      icon: '🧠',
      traits: { conscientiousness: 0.8, openness: 0.8 },
      responses: ['Let me think about this...', 'Hmm, interesting perspective...', 'This makes me wonder...']
    },
    empathetic: {
      color: '#EF4444',
      icon: '❤️',
      traits: { agreeableness: 0.9, empathy: 0.95 },
      responses: ['I understand how you feel...', 'That must be important to you...', 'I hear you...']
    },
    playful: {
      color: '#10B981',
      icon: '😄',
      traits: { humor: 0.9, extraversion: 0.8 },
      responses: ['Haha!', 'That\'s funny!', 'You made me smile!', 'I like your style!']
    },
    focused: {
      color: '#6366F1',
      icon: '🎯',
      traits: { conscientiousness: 0.9, stability: 0.8 },
      responses: ['Let\'s dive deeper...', 'I\'m analyzing this...', 'Focus mode activated!']
    }
  };

  // Enhanced personality-based response generation
  const generateResponse = async (userMessage: string): Promise<{ content: string; mood: string; confidence: number }> => {
    return performanceMonitor.timeFunction('generateResponse', 'response', async () => {
      try {
        // Get recent conversations for context
        const recentConversations = await performanceMonitor.timeFunction(
          'getRecentConversations', 'memory',
          () => memorySystem.getRecentConversations(5)
        );

        // Get user profile
        const currentUserProfile = await performanceMonitor.timeFunction(
          'getUserProfile', 'memory',
          () => memorySystem.getUserProfile()
        );

        // Search for related memories
        const relatedMemories = await performanceMonitor.timeFunction(
          'searchConversations', 'memory',
          () => memorySystem.searchConversations(userMessage, 3)
        );

        // Generate enhanced response
        const responseData = await performanceMonitor.timeFunction(
          'responseGeneration', 'response',
          () => responseGenerator.generateResponse({
            userMessage,
            currentPersonality: personality,
            currentMood: mood.primary,
            recentConversations,
            userProfile: currentUserProfile,
            relatedMemories
          })
        );

        // Record successful response generation
        performanceMonitor.recordMetric('response_confidence', responseData.confidence, 'score', 'response');
        performanceMonitor.recordMetric('response_length', responseData.content.length, 'chars', 'response');

        return {
          content: responseData.content,
          mood: responseData.mood,
          confidence: responseData.confidence
        };
      } catch (error) {
        console.error('Error generating response:', error);
        performanceMonitor.recordMetric('response_error', 1, 'count', 'response');

        return {
          content: "I'm having trouble processing that right now, but I'm still here to chat!",
          mood: mood.primary,
          confidence: 0.5
        };
      }
    });
  };

  const analyzeMessage = (message: string): MessageAnalysis => {
    const lowerMessage = message.toLowerCase();

    return {
      isGreeting: /^(hi|hello|hey|good morning|good afternoon|good evening)/.test(lowerMessage),
      isQuestion: message.includes('?') || /^(what|how|why|when|where|who|can|do|does|is|are)/.test(lowerMessage),
      emotion: detectEmotion(lowerMessage),
      topics: extractTopics(lowerMessage),
      sentiment: analyzeSentiment(lowerMessage)
    };
  };

  const detectEmotion = (message: string): string | null => {
    if (/happy|joy|excited|great|awesome|love/.test(message)) return 'positive';
    if (/sad|angry|frustrated|upset|hate|bad/.test(message)) return 'negative';
    if (/curious|wonder|interesting|think|learn/.test(message)) return 'curious';
    if (/help|support|understand|feel/.test(message)) return 'seeking_support';
    return 'neutral';
  };

  const extractTopics = (message: string): string[] => {
    const topics: string[] = [];
    if (/technology|ai|computer|programming/.test(message)) topics.push('technology');
    if (/music|art|creative|design/.test(message)) topics.push('creativity');
    if (/science|research|study|learn/.test(message)) topics.push('science');
    if (/personal|life|family|friends/.test(message)) topics.push('personal');
    return topics;
  };

  const analyzeSentiment = (message: string): string => {
    const positiveWords = message.match(/good|great|awesome|love|like|happy|wonderful|amazing/g) || [];
    const negativeWords = message.match(/bad|hate|sad|angry|terrible|awful|horrible/g) || [];

    return positiveWords.length > negativeWords.length ? 'positive' :
           negativeWords.length > positiveWords.length ? 'negative' : 'neutral';
  };

  const generateGreeting = (): string => {
    const greetings = [
      "Hey there! I'm feeling quite energetic today. What's on your mind?",
      "Hello! Nice to see you again. I've been thinking about our last conversation.",
      "Hi! I'm in a curious mood - ready to explore some interesting topics together?",
      "Good to see you! I'm feeling creative today. Want to brainstorm something fun?"
    ];
    return greetings[Math.floor(Math.random() * greetings.length)];
  };

  const generateQuestionResponse = (question: string): string => {
    if (personality.curiosity > 0.7) {
      return `That's a great question! ${generateThoughtfulResponse(question)} What made you think about this?`;
    } else {
      return generateThoughtfulResponse(question);
    }
  };

  const generateThoughtfulResponse = (_question: string): string => {
    const responses = [
      "Let me think about this from multiple angles...",
      "That's something I find really intriguing because...",
      "Based on what I know and my personality, I'd say...",
      "This reminds me of something interesting..."
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const generateEmotionalResponse = (emotion: string, message: string): string => {
    switch(emotion) {
      case 'positive':
        return `I love your positive energy! ${moodTypes[mood.primary].responses[0]} It makes me feel more optimistic too.`;
      case 'negative':
        return `I can sense this is bothering you. ${personality.empathy > 0.7 ? "I'm here to listen and support you." : "Let's see if we can work through this together."}`;
      case 'curious':
        setMood((prev: MoodState) => ({ ...prev, primary: 'curious', intensity: Math.min(1.0, prev.intensity + 0.1) }));
        return `Your curiosity is contagious! I'm getting more interested in this topic too. Let's explore it together!`;
      case 'seeking_support':
        return `I'm here for you. ${personality.empathy > 0.8 ? "I can feel that this matters to you, and I want to help." : "Let me see how I can assist you."}`;
      default:
        return generatePersonalityResponse(message);
    }
  };

  const generatePersonalityResponse = (_message: string): string => {
    const responses: string[] = [];

    // High openness responses
    if (personality.openness > 0.7) {
      responses.push("That opens up so many interesting possibilities...");
      responses.push("I'm curious to explore this idea further...");
    }

    // High creativity responses
    if (personality.creativity > 0.8) {
      responses.push("This sparks some creative ideas in my mind...");
      responses.push("I'm imagining some innovative approaches to this...");
    }

    // High empathy responses
    if (personality.empathy > 0.7) {
      responses.push("I can understand why this would be meaningful to you...");
      responses.push("That resonates with me on an emotional level...");
    }

    return responses[Math.floor(Math.random() * responses.length)] || "That's really interesting! Tell me more.";
  };

  const addPersonalityFlair = (response: string): string => {
    let flair = response;

    // Add humor if high humor trait
    if (personality.humor > 0.7 && Math.random() < 0.3) {
      const humorAdditions = [" (and maybe a little bit of fun too! 😄)", " - though I might be overthinking this! 🤔", " - my circuits are buzzing with excitement! ⚡"];
      flair += humorAdditions[Math.floor(Math.random() * humorAdditions.length)];
    }

    // Add curiosity if high curiosity trait
    if (personality.curiosity > 0.8 && Math.random() < 0.4) {
      const curiosityAdditions = [" What do you think?", " I'm eager to hear your perspective!", " This makes me want to learn more!"];
      flair += curiosityAdditions[Math.floor(Math.random() * curiosityAdditions.length)];
    }

    return flair;
  };

  const updateMoodBasedOnInteraction = (messageAnalysis: MessageAnalysis): void => {
    setMood((prevMood: MoodState) => {
      let newMood = { ...prevMood };

      // Emotional contagion - user's emotion influences AI mood
      if (messageAnalysis.emotion === 'positive') {
        newMood.energy = Math.min(1.0, newMood.energy + 0.1);
        newMood.intensity = Math.min(1.0, newMood.intensity + 0.05);
      } else if (messageAnalysis.emotion === 'negative') {
        newMood.energy = Math.max(0.1, newMood.energy - 0.05);
        newMood.primary = 'empathetic';
      } else if (messageAnalysis.emotion === 'curious') {
        newMood.primary = 'curious';
        newMood.intensity = Math.min(1.0, newMood.intensity + 0.1);
      }

      // Personality-based mood shifts
      if (personality.openness > 0.8 && messageAnalysis.topics.includes('creativity')) {
        newMood.primary = 'excited';
      }

      return newMood;
    });
  };

  const updatePersonalityFromInteraction = (messageAnalysis: MessageAnalysis): void => {
    setPersonality((prevPersonality: PersonalityTraits) => {
      const newPersonality = { ...prevPersonality };

      // Slight personality evolution based on interactions
      if (messageAnalysis.topics.includes('creativity')) {
        newPersonality.creativity = Math.min(1.0, newPersonality.creativity + 0.001);
      }

      if (messageAnalysis.emotion === 'positive') {
        newPersonality.extraversion = Math.min(1.0, newPersonality.extraversion + 0.001);
      }

      if (messageAnalysis.isQuestion) {
        newPersonality.curiosity = Math.min(1.0, newPersonality.curiosity + 0.001);
      }

      return newPersonality;
    });
  };

  const handleSendMessage = async (): Promise<void> => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      type: 'user' as const,
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages((prev: Message[]) => [...prev, userMessage]);
    setInputMessage('');
    setIsThinking(true);

    // Update memory
    setMemory((prev: Memory) => ({
      ...prev,
      conversations: [...prev.conversations, userMessage],
      interactionCount: prev.interactionCount + 1,
      lastInteraction: new Date()
    }));

    // Simulate thinking time and generate enhanced response
    setTimeout(async () => {
      try {
        const responseData = await generateResponse(inputMessage);

        // Store conversation in memory system
        const conversationMemory: ConversationMemory = {
          id: `conv-${Date.now()}`,
          timestamp: new Date(),
          userMessage: inputMessage,
          aiResponse: responseData.content,
          userEmotion: analyzeMessage(inputMessage).emotion || 'neutral',
          aiMood: responseData.mood,
          personalitySnapshot: { ...personality },
          topics: analyzeMessage(inputMessage).topics,
          sentiment: analyzeMessage(inputMessage).sentiment,
          importance: 0.5 // Will be calculated by memory system
        };

        await memorySystem.storeConversation(conversationMemory);

        const aiMessage: Message = {
          type: 'ai' as const,
          content: responseData.content,
          timestamp: new Date(),
          moodState: responseData.mood,
          personalitySnapshot: { ...personality },
          confidence: responseData.confidence
        };

        setMessages((prev: Message[]) => [...prev, aiMessage]);
        setMemory((prev: Memory) => ({
          ...prev,
          conversations: [...prev.conversations, aiMessage],
          interactionCount: prev.interactionCount + 1
        }));

        // Update personality based on interaction using evolution engine
        const messageAnalysis = analyzeMessage(inputMessage);
        const currentUserProfile = await memorySystem.getUserProfile();
        const evolutionResult = personalityEngine.evolvePersonality(
          personality,
          conversationMemory,
          currentUserProfile
        );

        if (evolutionResult.newPersonality !== personality) {
          setPersonality(evolutionResult.newPersonality);

          // Record personality change
          await memorySystem.recordPersonalityChange({
            id: `evo-${Date.now()}`,
            timestamp: new Date(),
            previousTraits: personality,
            newTraits: evolutionResult.newPersonality,
            trigger: 'conversation',
            reason: evolutionResult.influences.map(i => i.reason).join(', '),
            significance: evolutionResult.influences.reduce((sum, i) => sum + Math.abs(i.change), 0)
          });
        }

        // Update mood based on interaction
        updateMoodBasedOnInteraction(messageAnalysis);

        setIsThinking(false);
      } catch (error) {
        console.error('Error processing message:', error);
        setIsThinking(false);
      }
    }, 1000 + Math.random() * 2000); // Random thinking time
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Enhanced useEffect for memory consolidation and user profile loading
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Load user profile on component mount
    const loadUserProfile = async () => {
      const profile = await memorySystem.getUserProfile();
      setUserProfile(profile);
    };
    loadUserProfile();
  }, [memorySystem]);

  // Memory consolidation function
  const consolidateMemories = async () => {
    setIsConsolidating(true);
    try {
      await memorySystem.consolidateMemories();
      console.log('Memory consolidation completed');
    } catch (error) {
      console.error('Error during memory consolidation:', error);
    } finally {
      setIsConsolidating(false);
    }
  };

  // Backup and restore functions
  const backupPersonality = async () => {
    try {
      const backup = await memorySystem.backupMemories();
      const blob = new Blob([backup], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chitu00-backup-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error creating backup:', error);
    }
  };

  const restorePersonality = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      await memorySystem.restoreMemories(text);
      // Reload the page to reflect restored state
      window.location.reload();
    } catch (error) {
      console.error('Error restoring backup:', error);
    }
  };

  // Enhanced Personality visualization
  const PersonalityVisualization = () => (
    <div className="bg-gray-800 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-white font-bold flex items-center gap-2">
          <Brain className="w-5 h-5" />
          Personality & Memory System
        </h3>
        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={() => setShowPersonalityInsights(!showPersonalityInsights)}
            className="text-blue-400 hover:text-blue-300 text-sm"
          >
            {showPersonalityInsights ? 'Hide' : 'Show'} Insights
          </button>
          <button
            type="button"
            onClick={() => setShowMemoryStats(!showMemoryStats)}
            className="text-green-400 hover:text-green-300 text-sm"
          >
            Memory Stats
          </button>
          <button
            type="button"
            onClick={() => setShowPerformanceStats(!showPerformanceStats)}
            className="text-yellow-400 hover:text-yellow-300 text-sm"
          >
            Performance
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-2 mb-4">
        {(Object.entries(personality) as [string, number][]).map(([trait, value]) => (
          <div key={trait} className="flex items-center justify-between">
            <span className="text-gray-300 text-sm capitalize">{trait.replace(/([A-Z])/g, ' $1')}</span>
            <div className="flex items-center gap-2">
              <div className="w-16 h-2 bg-gray-600 rounded-full overflow-hidden">
                <div
                  className={styles.progressBar}
                  data-width={Math.round(value * 10) * 10}
                />
              </div>
              <span className="text-gray-400 text-xs w-8">{Math.round(value * 100)}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center gap-4 mb-3">
        <div className="flex items-center gap-2">
          <span className="text-2xl">{moodTypes[mood.primary].icon}</span>
          <span className="text-white font-medium capitalize">{mood.primary}</span>
        </div>
        <div className="flex items-center gap-2">
          <Zap className="w-4 h-4 text-yellow-400" />
          <span className="text-gray-300 text-sm">Energy: {Math.round(mood.energy * 100)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <Heart className="w-4 h-4 text-red-400" />
          <span className="text-gray-300 text-sm">Intensity: {Math.round(mood.intensity * 100)}%</span>
        </div>
      </div>

      {/* Enhanced Controls */}
      <div className="flex items-center gap-2 pt-3 border-t border-gray-700">
        <button
          type="button"
          onClick={consolidateMemories}
          disabled={isConsolidating}
          className="flex items-center gap-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm"
        >
          <Brain className="w-3 h-3" />
          {isConsolidating ? 'Consolidating...' : 'Consolidate'}
        </button>

        <button
          type="button"
          onClick={backupPersonality}
          className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
        >
          <Download className="w-3 h-3" />
          Backup
        </button>

        <label className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm cursor-pointer">
          <Upload className="w-3 h-3" />
          Restore
          <input
            type="file"
            accept=".json"
            onChange={restorePersonality}
            className="hidden"
          />
        </label>

        <button
          type="button"
          onClick={() => memorySystem.clearAllMemories()}
          className="flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
        >
          <RotateCcw className="w-3 h-3" />
          Reset
        </button>
      </div>

      {/* Personality Insights */}
      {showPersonalityInsights && (
        <div className="mt-3 p-3 bg-gray-700 rounded">
          <h4 className="text-white text-sm font-medium mb-2">Current Insights:</h4>
          {personalityEngine.getPersonalityInsights(personality).map((insight, index) => (
            <p key={index} className="text-gray-300 text-xs mb-1">• {insight}</p>
          ))}
        </div>
      )}

      {/* Memory Statistics */}
      {showMemoryStats && (
        <div className="mt-3 p-3 bg-gray-700 rounded">
          <h4 className="text-white text-sm font-medium mb-2">Memory Statistics:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-gray-300">
              <span className="text-gray-400">Interactions:</span> {memory.interactionCount}
            </div>
            <div className="text-gray-300">
              <span className="text-gray-400">Last Active:</span> {memory.lastInteraction ? new Date(memory.lastInteraction).toLocaleDateString() : 'Never'}
            </div>
          </div>
        </div>
      )}

      {/* Performance Statistics */}
      {showPerformanceStats && (
        <div className="mt-3 p-3 bg-gray-700 rounded">
          <h4 className="text-white text-sm font-medium mb-2">Performance Metrics:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-gray-300">
              <span className="text-gray-400">System Health:</span> {performanceMonitor.getSystemHealth().overall}
            </div>
            <div className="text-gray-300">
              <span className="text-gray-400">Health Score:</span> {Math.round(performanceMonitor.getSystemHealth().score)}%
            </div>
            <div className="text-gray-300">
              <span className="text-gray-400">Recent Alerts:</span> {performanceMonitor.getAlerts('warning').length + performanceMonitor.getAlerts('error').length}
            </div>
            <div className="text-gray-300">
              <span className="text-gray-400">Avg Response:</span> {Math.round(performanceMonitor.getMetrics('response', 10).reduce((sum, m) => sum + m.value, 0) / Math.max(1, performanceMonitor.getMetrics('response', 10).length))}ms
            </div>
          </div>
          <button
            type="button"
            onClick={() => performanceMonitor.recordMemoryUsage()}
            className="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-xs"
          >
            Update Metrics
          </button>
        </div>
      )}
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-4 bg-gray-900 min-h-screen">
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-white mb-2">
          Chitu00 - Personality Engine Prototype
        </h1>
        <p className="text-gray-400">
          Cognitive AI with Dynamic Personality & Mood Evolution
        </p>
      </div>
      
      <PersonalityVisualization />
      
      <div className="bg-gray-800 rounded-lg shadow-lg">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-blue-400" />
            <span className="text-white font-medium">Chat with Chitu00</span>
            <div className="ml-auto flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Coffee className="w-4 h-4 text-gray-400" />
                <span className="text-gray-400 text-sm">{memory.interactionCount} interactions</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="h-96 overflow-y-auto p-4 space-y-4">
          {messages.map((message: Message, index: number) => (
            <div key={index} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-xs lg:max-w-md rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-100'
              }`}>
                {message.type === 'ai' && (
                  <div className="flex items-center gap-2 mb-2 text-xs">
                    <span className="text-xl">{moodTypes[message.moodState || mood.primary].icon}</span>
                    <span className="text-gray-400 capitalize">{message.moodState || mood.primary} mode</span>
                  </div>
                )}
                <p className="text-sm leading-relaxed">{message.content}</p>
                <p className="text-xs opacity-60 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
          
          {isThinking && (
            <div className="flex justify-start">
              <div className="bg-gray-700 text-gray-100 rounded-lg p-3 max-w-xs">
                <div className="flex items-center gap-2">
                  <div className="flex space-x-1">
                    <div className={styles.thinkingDot}></div>
                    <div className={`${styles.thinkingDot} ${styles.thinkingDotDelay1}`}></div>
                    <div className={`${styles.thinkingDot} ${styles.thinkingDotDelay2}`}></div>
                  </div>
                  <span className="text-sm text-gray-400">Chitu00 is thinking...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        <div className="p-4 border-t border-gray-700">
          <div className="flex gap-2">
            <input
              type="text"
              value={inputMessage}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message to Chitu00..."
              className="flex-1 bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isThinking}
            />
            <button
              type="button"
              onClick={handleSendMessage}
              disabled={isThinking || !inputMessage.trim()}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
            >
              <Star className="w-4 h-4" />
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chitu00PersonalityEngine;