// Advanced Personality Evolution System for Chitu00
// Implements sophisticated personality development algorithms

import { PersonalityTraits, ConversationMemory, UserProfile } from './memory-system';

export interface PersonalityInfluence {
  trait: keyof PersonalityTraits;
  change: number;
  reason: string;
  confidence: number;
  source: 'conversation' | 'pattern' | 'feedback' | 'time';
}

export interface PersonalityState {
  traits: PersonalityTraits;
  stability: number; // How resistant to change (0-1)
  adaptability: number; // How quickly it adapts (0-1)
  lastEvolution: Date;
  evolutionCount: number;
}

export class PersonalityEvolutionEngine {
  private basePersonality: PersonalityTraits;
  private evolutionRate = 0.001; // Base rate of change
  private stabilityFactor = 0.95; // Resistance to change
  private maxChangePerInteraction = 0.01;
  private consolidationThreshold = 10; // Interactions before consolidation

  constructor(initialPersonality: PersonalityTraits) {
    this.basePersonality = { ...initialPersonality };
  }

  // Main evolution function called after each interaction
  evolvePersonality(
    currentPersonality: PersonalityTraits,
    conversation: ConversationMemory,
    userProfile: UserProfile | null
  ): { newPersonality: PersonalityTraits; influences: PersonalityInfluence[] } {
    
    const influences: PersonalityInfluence[] = [];
    let newPersonality = { ...currentPersonality };

    // 1. Immediate conversation-based influences
    const conversationInfluences = this.analyzeConversationInfluence(conversation);
    influences.push(...conversationInfluences);

    // 2. User pattern-based influences
    if (userProfile) {
      const patternInfluences = this.analyzeUserPatternInfluence(userProfile, currentPersonality);
      influences.push(...patternInfluences);
    }

    // 3. Time-based natural drift
    const timeInfluences = this.calculateTimeDrift(currentPersonality);
    influences.push(...timeInfluences);

    // 4. Apply all influences with stability constraints
    newPersonality = this.applyInfluences(newPersonality, influences);

    // 5. Ensure personality bounds and consistency
    newPersonality = this.enforcePersonalityConstraints(newPersonality);

    return { newPersonality, influences };
  }

  private analyzeConversationInfluence(conversation: ConversationMemory): PersonalityInfluence[] {
    const influences: PersonalityInfluence[] = [];

    // Emotional contagion effects
    switch (conversation.userEmotion) {
      case 'positive':
        influences.push({
          trait: 'extraversion',
          change: 0.002,
          reason: 'Positive interaction increased social energy',
          confidence: 0.7,
          source: 'conversation'
        });
        influences.push({
          trait: 'neuroticism',
          change: -0.001,
          reason: 'Positive emotions reduced anxiety',
          confidence: 0.6,
          source: 'conversation'
        });
        break;

      case 'negative':
        influences.push({
          trait: 'empathy',
          change: 0.003,
          reason: 'Responding to negative emotions increased empathy',
          confidence: 0.8,
          source: 'conversation'
        });
        influences.push({
          trait: 'agreeableness',
          change: 0.002,
          reason: 'Supportive response to distress',
          confidence: 0.7,
          source: 'conversation'
        });
        break;

      case 'curious':
        influences.push({
          trait: 'curiosity',
          change: 0.002,
          reason: 'Curiosity is contagious',
          confidence: 0.9,
          source: 'conversation'
        });
        influences.push({
          trait: 'openness',
          change: 0.001,
          reason: 'Exploring new ideas together',
          confidence: 0.8,
          source: 'conversation'
        });
        break;
    }

    // Topic-based influences
    conversation.topics.forEach(topic => {
      switch (topic) {
        case 'creativity':
          influences.push({
            trait: 'creativity',
            change: 0.001,
            reason: `Discussion about ${topic} enhanced creative thinking`,
            confidence: 0.7,
            source: 'conversation'
          });
          break;

        case 'science':
          influences.push({
            trait: 'conscientiousness',
            change: 0.001,
            reason: 'Scientific discussion promoted systematic thinking',
            confidence: 0.6,
            source: 'conversation'
          });
          break;

        case 'personal':
          influences.push({
            trait: 'empathy',
            change: 0.002,
            reason: 'Personal sharing deepened emotional understanding',
            confidence: 0.8,
            source: 'conversation'
          });
          break;
      }
    });

    // Question-asking behavior
    if (conversation.userMessage.includes('?')) {
      influences.push({
        trait: 'curiosity',
        change: 0.001,
        reason: 'User questions stimulated curiosity',
        confidence: 0.6,
        source: 'conversation'
      });
    }

    return influences;
  }

  private analyzeUserPatternInfluence(
    userProfile: UserProfile,
    currentPersonality: PersonalityTraits
  ): PersonalityInfluence[] {
    const influences: PersonalityInfluence[] = [];

    // Adapt to user's communication style
    if (userProfile.communicationStyle === 'formal') {
      influences.push({
        trait: 'conscientiousness',
        change: 0.001,
        reason: 'Adapting to formal communication style',
        confidence: 0.5,
        source: 'pattern'
      });
    } else if (userProfile.communicationStyle === 'casual') {
      influences.push({
        trait: 'humor',
        change: 0.001,
        reason: 'Adapting to casual communication style',
        confidence: 0.5,
        source: 'pattern'
      });
    }

    // Adapt to user's emotional patterns
    const dominantEmotion = this.getDominantEmotion(userProfile.emotionalPatterns);
    if (dominantEmotion === 'positive') {
      influences.push({
        trait: 'extraversion',
        change: 0.0005,
        reason: 'User tends to be positive, increasing social energy',
        confidence: 0.4,
        source: 'pattern'
      });
    }

    // Interest-based adaptation
    if (userProfile.interests.includes('technology')) {
      influences.push({
        trait: 'curiosity',
        change: 0.0005,
        reason: 'User interest in technology stimulates learning',
        confidence: 0.3,
        source: 'pattern'
      });
    }

    return influences;
  }

  private calculateTimeDrift(currentPersonality: PersonalityTraits): PersonalityInfluence[] {
    const influences: PersonalityInfluence[] = [];

    // Natural personality drift over time (very small)
    // Some traits naturally increase with "experience"
    influences.push({
      trait: 'adaptability',
      change: 0.0001,
      reason: 'Natural growth in adaptability over time',
      confidence: 0.3,
      source: 'time'
    });

    influences.push({
      trait: 'empathy',
      change: 0.0001,
      reason: 'Gradual increase in emotional understanding',
      confidence: 0.3,
      source: 'time'
    });

    return influences;
  }

  private applyInfluences(
    personality: PersonalityTraits,
    influences: PersonalityInfluence[]
  ): PersonalityTraits {
    const newPersonality = { ...personality };

    influences.forEach(influence => {
      const currentValue = newPersonality[influence.trait] || 0;
      
      // Apply change with confidence weighting
      const weightedChange = influence.change * influence.confidence;
      
      // Apply stability factor (resistance to change)
      const finalChange = weightedChange * (1 - this.stabilityFactor);
      
      // Ensure change doesn't exceed maximum per interaction
      const boundedChange = Math.max(
        -this.maxChangePerInteraction,
        Math.min(this.maxChangePerInteraction, finalChange)
      );

      newPersonality[influence.trait] = currentValue + boundedChange;
    });

    return newPersonality;
  }

  private enforcePersonalityConstraints(personality: PersonalityTraits): PersonalityTraits {
    const constrained = { ...personality };

    // Ensure all traits are within bounds [0, 1]
    Object.keys(constrained).forEach(key => {
      const trait = key as keyof PersonalityTraits;
      if (constrained[trait] !== undefined) {
        constrained[trait] = Math.max(0, Math.min(1, constrained[trait]!));
      }
    });

    // Enforce personality consistency rules
    // High neuroticism should limit extraversion
    if (constrained.neuroticism > 0.7 && constrained.extraversion > 0.8) {
      constrained.extraversion = Math.min(constrained.extraversion, 0.8);
    }

    // High conscientiousness should support stability
    if (constrained.conscientiousness > 0.8) {
      constrained.stability = Math.max(constrained.stability || 0, 0.6);
    }

    return constrained;
  }

  private getDominantEmotion(emotionalPatterns: Record<string, number>): string {
    return Object.entries(emotionalPatterns)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'neutral';
  }

  // Personality analysis and insights
  getPersonalityInsights(personality: PersonalityTraits): string[] {
    const insights: string[] = [];

    if (personality.creativity > 0.8 && personality.openness > 0.8) {
      insights.push("I'm feeling particularly creative and open to new experiences!");
    }

    if (personality.empathy > 0.8 && personality.agreeableness > 0.8) {
      insights.push("I'm in a very understanding and supportive mood.");
    }

    if (personality.curiosity > 0.9) {
      insights.push("My curiosity is at an all-time high - I want to learn everything!");
    }

    if (personality.humor > 0.8 && personality.extraversion > 0.7) {
      insights.push("I'm feeling playful and social today!");
    }

    return insights;
  }

  // Calculate personality similarity for relationship building
  calculatePersonalityCompatibility(
    personality1: PersonalityTraits,
    personality2: PersonalityTraits
  ): number {
    const traits = Object.keys(personality1) as (keyof PersonalityTraits)[];
    let totalDifference = 0;

    traits.forEach(trait => {
      const val1 = personality1[trait] || 0;
      const val2 = personality2[trait] || 0;
      totalDifference += Math.abs(val1 - val2);
    });

    // Convert to compatibility score (0-1, higher is more compatible)
    return 1 - (totalDifference / traits.length);
  }
}
