// Debug and Diagnostics Tool for Chitu00 Phase 1
// Comprehensive system analysis and issue detection

import { PersonalityTraits, ConversationMemory, UserProfile } from './memory-system';
import { FileMemorySystem } from './file-memory-system';
import { PersonalityEvolutionEngine } from './personality-evolution';

export interface DiagnosticReport {
  timestamp: Date;
  systemHealth: 'healthy' | 'warning' | 'critical';
  issues: DiagnosticIssue[];
  recommendations: string[];
  performance: PerformanceMetrics;
  memoryAnalysis: MemoryAnalysis;
  personalityAnalysis: PersonalityAnalysis;
}

export interface DiagnosticIssue {
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'memory' | 'personality' | 'performance' | 'data' | 'ui';
  description: string;
  solution: string;
  affectedComponents: string[];
}

export interface PerformanceMetrics {
  memoryUsage: number; // in bytes
  responseTime: number; // in milliseconds
  storageEfficiency: number; // percentage
  evolutionRate: number; // changes per interaction
  errorRate: number; // percentage
}

export interface MemoryAnalysis {
  totalConversations: number;
  averageImportance: number;
  topicDistribution: Record<string, number>;
  emotionDistribution: Record<string, number>;
  memoryFragmentation: number;
  oldestMemory: Date | null;
  newestMemory: Date | null;
}

export interface PersonalityAnalysis {
  currentTraits: PersonalityTraits;
  traitStability: Record<string, number>;
  evolutionTrend: 'stable' | 'evolving' | 'volatile';
  dominantTraits: string[];
  weakTraits: string[];
  inconsistencies: string[];
}

export class DiagnosticEngine {
  private memorySystem: FileMemorySystem;
  private personalityEngine: PersonalityEvolutionEngine;

  constructor(memorySystem: FileMemorySystem, personalityEngine: PersonalityEvolutionEngine) {
    this.memorySystem = memorySystem;
    this.personalityEngine = personalityEngine;
  }

  async generateDiagnosticReport(): Promise<DiagnosticReport> {
    const issues: DiagnosticIssue[] = [];
    const recommendations: string[] = [];

    // Analyze memory system
    const memoryAnalysis = await this.analyzeMemorySystem();
    issues.push(...this.detectMemoryIssues(memoryAnalysis));

    // Analyze personality system
    const personalityAnalysis = await this.analyzePersonalitySystem();
    issues.push(...this.detectPersonalityIssues(personalityAnalysis));

    // Analyze performance
    const performance = await this.analyzePerformance();
    issues.push(...this.detectPerformanceIssues(performance));

    // Generate recommendations
    recommendations.push(...this.generateRecommendations(issues));

    // Determine overall system health
    const systemHealth = this.determineSystemHealth(issues);

    return {
      timestamp: new Date(),
      systemHealth,
      issues,
      recommendations,
      performance,
      memoryAnalysis,
      personalityAnalysis
    };
  }

  private async analyzeMemorySystem(): Promise<MemoryAnalysis> {
    const conversations = await this.memorySystem.getRecentConversations(1000);
    
    if (conversations.length === 0) {
      return {
        totalConversations: 0,
        averageImportance: 0,
        topicDistribution: {},
        emotionDistribution: {},
        memoryFragmentation: 0,
        oldestMemory: null,
        newestMemory: null
      };
    }

    // Calculate topic distribution
    const topicDistribution: Record<string, number> = {};
    conversations.forEach(conv => {
      conv.topics.forEach(topic => {
        topicDistribution[topic] = (topicDistribution[topic] || 0) + 1;
      });
    });

    // Calculate emotion distribution
    const emotionDistribution: Record<string, number> = {};
    conversations.forEach(conv => {
      emotionDistribution[conv.userEmotion] = (emotionDistribution[conv.userEmotion] || 0) + 1;
    });

    // Calculate average importance
    const averageImportance = conversations.reduce((sum, conv) => sum + conv.importance, 0) / conversations.length;

    // Find oldest and newest memories
    const timestamps = conversations.map(conv => new Date(conv.timestamp));
    const oldestMemory = new Date(Math.min(...timestamps.map(d => d.getTime())));
    const newestMemory = new Date(Math.max(...timestamps.map(d => d.getTime())));

    // Calculate memory fragmentation (simplified)
    const memoryFragmentation = this.calculateMemoryFragmentation(conversations);

    return {
      totalConversations: conversations.length,
      averageImportance,
      topicDistribution,
      emotionDistribution,
      memoryFragmentation,
      oldestMemory,
      newestMemory
    };
  }

  private async analyzePersonalitySystem(): Promise<PersonalityAnalysis> {
    const personalityHistory = await this.memorySystem.getPersonalityHistory(50);
    
    if (personalityHistory.length === 0) {
      // Return default analysis for new system
      return {
        currentTraits: {
          openness: 0.8,
          conscientiousness: 0.7,
          extraversion: 0.6,
          agreeableness: 0.8,
          neuroticism: 0.3,
          creativity: 0.9,
          humor: 0.7,
          curiosity: 0.85,
          empathy: 0.8,
          adaptability: 0.75
        },
        traitStability: {},
        evolutionTrend: 'stable',
        dominantTraits: ['creativity', 'curiosity', 'empathy'],
        weakTraits: ['neuroticism'],
        inconsistencies: []
      };
    }

    const currentTraits = personalityHistory[personalityHistory.length - 1].newTraits;
    
    // Calculate trait stability
    const traitStability: Record<string, number> = {};
    Object.keys(currentTraits).forEach(trait => {
      const traitKey = trait as keyof PersonalityTraits;
      const values = personalityHistory.map(h => h.newTraits[traitKey] || 0);
      const variance = this.calculateVariance(values);
      traitStability[trait] = 1 - variance; // Higher stability = lower variance
    });

    // Determine evolution trend
    const recentChanges = personalityHistory.slice(-10);
    const totalChange = recentChanges.reduce((sum, change) => sum + change.significance, 0);
    const evolutionTrend = totalChange > 0.1 ? 'volatile' : totalChange > 0.05 ? 'evolving' : 'stable';

    // Find dominant and weak traits
    const traitEntries = Object.entries(currentTraits) as [string, number][];
    const sortedTraits = traitEntries.sort(([,a], [,b]) => b - a);
    const dominantTraits = sortedTraits.slice(0, 3).map(([trait]) => trait);
    const weakTraits = sortedTraits.slice(-2).map(([trait]) => trait);

    // Detect inconsistencies
    const inconsistencies = this.detectPersonalityInconsistencies(currentTraits);

    return {
      currentTraits,
      traitStability,
      evolutionTrend,
      dominantTraits,
      weakTraits,
      inconsistencies
    };
  }

  private async analyzePerformance(): Promise<PerformanceMetrics> {
    const startTime = performance.now();
    
    // Test memory operations
    await this.memorySystem.getRecentConversations(10);
    const memoryOpTime = performance.now() - startTime;

    // Calculate memory usage (approximate)
    let memoryUsage = 0;
    if (typeof window !== 'undefined') {
      for (let key in localStorage) {
        if (key.startsWith('chitu00-')) {
          memoryUsage += localStorage[key].length * 2; // Approximate bytes (UTF-16)
        }
      }
    }

    // Calculate storage efficiency
    const conversations = await this.memorySystem.getRecentConversations(100);
    const storageSize = JSON.stringify(conversations).length;
    const storageEfficiency = conversations.length > 0 ? (conversations.length / storageSize) * 1000 : 100;

    // Calculate evolution rate
    const personalityHistory = await this.memorySystem.getPersonalityHistory(20);
    const evolutionRate = personalityHistory.length > 0 ? 
      personalityHistory.reduce((sum, h) => sum + h.significance, 0) / personalityHistory.length : 0;

    return {
      memoryUsage,
      responseTime: memoryOpTime,
      storageEfficiency,
      evolutionRate,
      errorRate: 0 // Would be calculated from error logs in production
    };
  }

  private detectMemoryIssues(analysis: MemoryAnalysis): DiagnosticIssue[] {
    const issues: DiagnosticIssue[] = [];

    // Check for insufficient data
    if (analysis.totalConversations < 5) {
      issues.push({
        severity: 'medium',
        category: 'memory',
        description: 'Insufficient conversation data for optimal learning',
        solution: 'Engage in more conversations to improve AI learning',
        affectedComponents: ['memory-system', 'personality-evolution']
      });
    }

    // Check for memory fragmentation
    if (analysis.memoryFragmentation > 0.7) {
      issues.push({
        severity: 'medium',
        category: 'memory',
        description: 'High memory fragmentation detected',
        solution: 'Run memory consolidation to optimize storage',
        affectedComponents: ['memory-system']
      });
    }

    // Check for topic imbalance
    const topicCounts = Object.values(analysis.topicDistribution);
    if (topicCounts.length > 0) {
      const maxCount = Math.max(...topicCounts);
      const minCount = Math.min(...topicCounts);
      if (maxCount / minCount > 5) {
        issues.push({
          severity: 'low',
          category: 'data',
          description: 'Unbalanced topic distribution in conversations',
          solution: 'Engage in more diverse conversation topics',
          affectedComponents: ['response-generator', 'personality-evolution']
        });
      }
    }

    return issues;
  }

  private detectPersonalityIssues(analysis: PersonalityAnalysis): DiagnosticIssue[] {
    const issues: DiagnosticIssue[] = [];

    // Check for trait extremes
    Object.entries(analysis.currentTraits).forEach(([trait, value]) => {
      if (value < 0.1 || value > 0.95) {
        issues.push({
          severity: 'medium',
          category: 'personality',
          description: `Extreme ${trait} value (${Math.round(value * 100)}%) may affect behavior`,
          solution: 'Monitor personality evolution and consider manual adjustment if needed',
          affectedComponents: ['personality-evolution', 'response-generator']
        });
      }
    });

    // Check for inconsistencies
    if (analysis.inconsistencies.length > 0) {
      issues.push({
        severity: 'medium',
        category: 'personality',
        description: `Personality inconsistencies detected: ${analysis.inconsistencies.join(', ')}`,
        solution: 'Review personality constraints and evolution rules',
        affectedComponents: ['personality-evolution']
      });
    }

    // Check for volatility
    if (analysis.evolutionTrend === 'volatile') {
      issues.push({
        severity: 'high',
        category: 'personality',
        description: 'Personality evolution is too volatile',
        solution: 'Reduce evolution rate or increase stability factors',
        affectedComponents: ['personality-evolution']
      });
    }

    return issues;
  }

  private detectPerformanceIssues(performance: PerformanceMetrics): DiagnosticIssue[] {
    const issues: DiagnosticIssue[] = [];

    // Check response time
    if (performance.responseTime > 100) {
      issues.push({
        severity: 'medium',
        category: 'performance',
        description: `Slow response time (${Math.round(performance.responseTime)}ms)`,
        solution: 'Optimize memory operations or reduce data size',
        affectedComponents: ['memory-system', 'response-generator']
      });
    }

    // Check memory usage
    if (performance.memoryUsage > 1024 * 1024) { // 1MB
      issues.push({
        severity: 'medium',
        category: 'performance',
        description: `High memory usage (${Math.round(performance.memoryUsage / 1024)}KB)`,
        solution: 'Run memory consolidation or clear old data',
        affectedComponents: ['memory-system']
      });
    }

    // Check storage efficiency
    if (performance.storageEfficiency < 1) {
      issues.push({
        severity: 'low',
        category: 'performance',
        description: 'Low storage efficiency detected',
        solution: 'Optimize data structures or compression',
        affectedComponents: ['memory-system']
      });
    }

    return issues;
  }

  private generateRecommendations(issues: DiagnosticIssue[]): string[] {
    const recommendations: string[] = [];

    const criticalIssues = issues.filter(i => i.severity === 'critical');
    const highIssues = issues.filter(i => i.severity === 'high');
    const mediumIssues = issues.filter(i => i.severity === 'medium');

    if (criticalIssues.length > 0) {
      recommendations.push('🚨 Address critical issues immediately to prevent system failure');
    }

    if (highIssues.length > 0) {
      recommendations.push('⚠️ High priority issues detected - review personality evolution settings');
    }

    if (mediumIssues.length > 2) {
      recommendations.push('🔧 Multiple medium issues found - consider running system maintenance');
    }

    if (issues.some(i => i.category === 'memory')) {
      recommendations.push('🧠 Run memory consolidation to optimize storage and performance');
    }

    if (issues.some(i => i.category === 'performance')) {
      recommendations.push('⚡ Performance optimization recommended - clear old data or reduce complexity');
    }

    if (issues.length === 0) {
      recommendations.push('✅ System is running optimally - no immediate action required');
    }

    return recommendations;
  }

  private determineSystemHealth(issues: DiagnosticIssue[]): 'healthy' | 'warning' | 'critical' {
    const criticalCount = issues.filter(i => i.severity === 'critical').length;
    const highCount = issues.filter(i => i.severity === 'high').length;
    const mediumCount = issues.filter(i => i.severity === 'medium').length;

    if (criticalCount > 0) return 'critical';
    if (highCount > 0 || mediumCount > 3) return 'warning';
    return 'healthy';
  }

  private calculateMemoryFragmentation(conversations: ConversationMemory[]): number {
    // Simplified fragmentation calculation based on timestamp gaps
    if (conversations.length < 2) return 0;

    const timestamps = conversations.map(c => new Date(c.timestamp).getTime()).sort();
    const gaps = [];
    
    for (let i = 1; i < timestamps.length; i++) {
      gaps.push(timestamps[i] - timestamps[i-1]);
    }

    const avgGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;
    const variance = gaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / gaps.length;
    
    return Math.min(1, variance / (avgGap * avgGap)); // Normalized fragmentation score
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  private detectPersonalityInconsistencies(traits: PersonalityTraits): string[] {
    const inconsistencies: string[] = [];

    // Check for logical inconsistencies
    if (traits.neuroticism > 0.8 && traits.extraversion > 0.8) {
      inconsistencies.push('High neuroticism with high extraversion');
    }

    if (traits.conscientiousness < 0.3 && traits.agreeableness > 0.8) {
      inconsistencies.push('Low conscientiousness with high agreeableness');
    }

    if (traits.openness < 0.3 && traits.creativity > 0.8) {
      inconsistencies.push('Low openness with high creativity');
    }

    return inconsistencies;
  }
}
